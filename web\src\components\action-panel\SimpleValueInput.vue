<template>
  <div class="simple-value-input" @click="onEdit">
    <div class="value-content" :class="{ 'has-error': hasError }">
      <!-- 文本值 -->
      <span v-if="dataValue?.type === 'text'" class="value-text">
        {{ dataValue.textValue || placeholder }}
      </span>

      <!-- 变量值 -->
      <span v-else-if="dataValue?.type === 'variable'" class="value-text variable" :class="{ invalid: hasError }">
        <t-icon name="link" class="value-icon" />
        {{ displayText }}
        <t-icon v-if="hasError" name="error-circle" class="error-icon" />
      </span>

      <!-- 脚本值 -->
      <span v-else-if="dataValue?.type === 'script'" class="value-text script">
        <t-icon name="code" class="value-icon" />
        {{ dataValue.scriptName || '点击编写脚本' }}
      </span>

      <!-- 可视化值 -->
      <span v-else-if="dataValue?.type === 'visual'" class="value-text visual">
        <t-icon name="view-module" class="value-icon" />
        {{ dataValue.visualName || `${dataValue.visualSteps?.length || 0} 个步骤` }}
      </span>

      <!-- 默认状态 -->
      <span v-else class="value-text placeholder">
        {{ placeholder }}
      </span>
    </div>

    <!-- 类型标识（可选） -->
    <div v-if="showTypeIndicator && typeText" class="type-badge">
      {{ typeText }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, watch, ref, onMounted, onActivated } from 'vue';
import { FlowData, FlowDataValue } from './model';
import { useVariableReference } from './composables/useVariableReference';
import { useActionFlowStore } from './store';
import { getRandomId, getGlobalVariables } from './utils';
import { debugVariables } from './utils/variableProcessor';

interface Props {
  dataValue?: FlowDataValue;
  dataType?: string;
  onlyVariable?: boolean;
  limitTypes?: string[];
  placeholder?: string;
  showTypeIndicator?: boolean;
  availableVariables?: FlowData[];
}

interface Emits {
  (e: 'update:data-value', value: FlowDataValue): void;
  (e: 'variable-change', value: FlowDataValue): void;
  (e: 'edit'): void;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '点击设置值',
  showTypeIndicator: false,
});

const emits = defineEmits<Emits>();

const actionFlowStore = useActionFlowStore();

// 创建默认值
const createDefaultValue = (): FlowDataValue => ({
  type: props.onlyVariable ? 'variable' : 'text',
  dataType: props.dataType || props.limitTypes?.join(',') || '',
  textValue: '',
  variableType: '',
  variableName: '',
  variableValue: '',
  scriptName: '',
  scriptValue: '',
  visualSteps: [],
  visualName: '',
});

// 全局变量缓存
const globalVariables = ref<FlowData[]>([]);

// 初始化全局变量
const initializeGlobalVariables = async () => {
  try {
    globalVariables.value = await getGlobalVariables();
  } catch (error) {
    console.error('SimpleValueInput: 获取全局变量失败:', error);
    globalVariables.value = [];
  }
};

// 组件挂载时初始化
onMounted(async () => {
  console.log('SimpleValueInput 组件挂载:', {
    dataValue: props.dataValue,
    variableType: props.dataValue?.variableType,
    variableValue: props.dataValue?.variableValue,
    type: props.dataValue?.type,
  });
  await initializeGlobalVariables();

  // 挂载后强制触发一次变量引用更新
  if (props.dataValue?.type === 'variable') {
    console.log('组件挂载后触发变量引用更新');
    updateVariableReference();
  }
});
onActivated(async () => {
  console.log('SimpleValueInput 组件激活:', {
    dataValue: props.dataValue,
    variableType: props.dataValue?.variableType,
    variableValue: props.dataValue?.variableValue,
    type: props.dataValue?.type,
  });
  await initializeGlobalVariables();

  // 激活后强制触发一次变量引用更新
  if (props.dataValue?.type === 'variable') {
    console.log('组件激活后触发变量引用更新');
    updateVariableReference();
  }
});

// 检测是否需要自动解析变量引用
const shouldAutoResolveVariables = computed(() => {
  const dataValue = props.dataValue;

  console.log('shouldAutoResolveVariables 检测:', {
    hasDataValue: !!dataValue,
    dataValueType: dataValue?.type,
    variableType: dataValue?.variableType,
    variableRefId: dataValue?.variableRefId,
    variableValue: dataValue?.variableValue,
    variableName: dataValue?.variableName,
    hasPropsAvailableVariables: !!(props.availableVariables && props.availableVariables.length > 0),
  });

  // 如果已经提供了 availableVariables，则不需要自动解析
  if (props.availableVariables && props.availableVariables.length > 0) {
    console.log('已提供 availableVariables，跳过自动解析');
    return false;
  }

  // 只有变量类型才需要自动解析
  if (dataValue?.type !== 'variable') {
    console.log('非变量类型，不需要自动解析');
    return false;
  }

  // 情况1: 有 variableType 的变量
  if (dataValue.variableType) {
    // 检查是否需要重新解析：
    // 1. 没有 variableRefId
    // 2. 没有 variableValue
    // 3. 有 variableValue 但需要验证引用是否有效
    const hasVariableRefId = !!dataValue.variableRefId;
    const hasVariableValue = !!dataValue.variableValue;

    // 如果没有基本的引用信息，肯定需要解析
    if (!hasVariableRefId || !hasVariableValue) {
      const shouldResolve = true;
      console.log('缺少基本引用信息，需要解析:', {
        variableType: dataValue.variableType,
        hasVariableRefId,
        hasVariableValue,
        shouldResolve,
      });
      return shouldResolve;
    }

    // 如果有引用信息，检查引用是否有效
    // 通过获取对应类型的变量列表来验证
    const variables = getAvailableVariablesByType(dataValue.variableType);
    const isValidReference = variables.some((variable) => {
      const variablePath = variable.path || variable.key;
      return variablePath === dataValue.variableValue || variable.key === dataValue.variableValue;
    });

    const shouldResolve = !isValidReference;
    console.log('检查引用有效性:', {
      variableType: dataValue.variableType,
      variableValue: dataValue.variableValue,
      variablesCount: variables.length,
      isValidReference,
      shouldResolve,
    });
    return shouldResolve;
  }

  // 情况2: 没有 variableType 的变量，也需要自动解析以显示所有变量供选择
  console.log('没有 variableType，需要自动解析显示所有变量');
  return true;
});

// 根据 variableType 获取对应的可用变量列表（使用 store 中的处理逻辑）
const getAvailableVariablesByType = (variableType?: string): FlowData[] => {
  const processedVariables = actionFlowStore.getProcessedVariablesByType(variableType, globalVariables.value);

  // 使用工具函数输出调试信息
  debugVariables(processedVariables, `getAvailableVariablesByType(${variableType || 'all'})`);

  return processedVariables;
};

// 动态计算可用变量列表
const availableVariables = computed(() => {
  const dataValue = props.dataValue;

  console.log('availableVariables 计算触发:', {
    hasPropsAvailableVariables: !!(props.availableVariables && props.availableVariables.length > 0),
    shouldAutoResolve: shouldAutoResolveVariables.value,
    dataValueType: dataValue?.type,
    variableType: dataValue?.variableType,
    variableValue: dataValue?.variableValue,
  });

  // 如果已经提供了 availableVariables，直接使用
  if (props.availableVariables && props.availableVariables.length > 0) {
    console.log('使用提供的 availableVariables');
    return props.availableVariables;
  }

  // 如果需要自动解析，根据 variableType 获取对应的变量列表
  if (shouldAutoResolveVariables.value && dataValue?.type === 'variable') {
    const variableType = dataValue.variableType;

    // 如果有具体的 variableType，获取对应类型的变量
    if (variableType) {
      const variables = getAvailableVariablesByType(variableType);

      // 调试信息
      console.log('SimpleValueInput: 自动解析特定类型变量', {
        variableType,
        variablesCount: variables.length,
        variableValue: dataValue.variableValue,
        variableName: dataValue.variableName,
        hasVariableRefId: !!dataValue.variableRefId,
        variables: variables.map((v) => ({
          key: v.key,
          path: v.path,
          type: v.type,
          description: v.description,
        })),
      });

      return variables;
    } else {
      // 如果没有 variableType，返回所有变量供选择
      const allVariables = getAvailableVariablesByType();

      console.log('SimpleValueInput: 自动解析所有变量', {
        variablesCount: allVariables.length,
        currentCount: actionFlowStore.currentVariables?.length || 0,
        localCount: actionFlowStore.localVariables?.length || 0,
        globalCount: globalVariables.value?.length || 0,
      });

      return allVariables;
    }
  }

  // 默认返回空数组
  console.log('返回空数组，不满足自动解析条件');
  return [];
});

// 使用变量引用管理（支持自动初始化）
const { resolvedVariableInfo, isValidReference, variableType, updateVariableReference } = useVariableReference(
  computed(() => props.dataValue || createDefaultValue()),
  availableVariables,
);

// 计算显示文本
const displayText = computed(() => {
  if (props.dataValue?.type === 'variable') {
    // 优先使用解析后的变量信息
    if (isValidReference.value && resolvedVariableInfo.value) {
      return resolvedVariableInfo.value.name || resolvedVariableInfo.value.path;
    }
    // 降级到原有的显示方式
    return props.dataValue.variableName || props.dataValue.variableValue || '点击选择变量';
  }
  return '';
});

// 检查是否有错误
const hasError = computed(() => {
  return props.dataValue?.type === 'variable' && !isValidReference.value && !!props.dataValue.variableValue;
});

// 获取类型文本
const typeText = computed(() => {
  const type = variableType.value || props.dataValue?.dataType || props.dataType;
  if (!type) return '';

  const typeMap: Record<string, string> = {
    string: '文本',
    number: '数字',
    int: '整数',
    decimal: '小数',
    boolean: '布尔',
    object: '对象',
    array: '数组',
    DateTime: '日期',
  };
  return typeMap[type] || type;
});

// 编辑按钮点击
const onEdit = () => {
  actionFlowStore.isSaveValue = false;
  actionFlowStore.showValueDialog = true;
  const valueId = getRandomId();
  actionFlowStore.currentValueInputData = {
    id: valueId,
    value: props.dataValue ? { ...props.dataValue } : createDefaultValue(),
    onlyVariable: props.onlyVariable,
    limitTypes: props.limitTypes,
  };
  emits('edit');
};

// 监听 store 中的保存事件
watch(
  () => actionFlowStore.isSaveValue,
  (isSave) => {
    if (isSave && actionFlowStore.currentValueInputData?.value) {
      const newValue = actionFlowStore.currentValueInputData.value;
      emits('update:data-value', newValue);
      emits('variable-change', newValue);
      actionFlowStore.isSaveValue = false;
    }
  },
);

// 监听变量引用变化
watch(
  () => resolvedVariableInfo.value,
  (newInfo) => {
    console.log('resolvedVariableInfo 变化:', {
      newInfo,
      dataValueType: props.dataValue?.type,
      dataValue: props.dataValue,
    });

    if (newInfo && props.dataValue?.type === 'variable') {
      // 当变量信息更新时，触发变更事件
      emits('variable-change', props.dataValue);
    }
  },
  { deep: true },
);
</script>

<style lang="less" scoped>
.simple-value-input {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 12px;
  // border: 1px solid var(--td-border-level-1-color);
  border-radius: 3px;
  background: var(--td-bg-color-container);
  min-height: 24px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: var(--td-border-level-2-color);
    background: var(--td-bg-color-container-hover);
  }

  &:active {
    border-color: var(--td-brand-color);
  }

  .value-content {
    flex: 1;
    display: flex;
    align-items: center;
    min-height: 20px;

    &.has-error {
      .value-text.invalid {
        color: var(--td-error-color);
      }
    }

    .value-text {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      color: var(--td-text-color-primary);
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;

      &.placeholder {
        color: var(--td-text-color-placeholder);
      }

      &.variable {
        color: var(--td-brand-color);

        &.invalid {
          color: var(--td-error-color);
          text-decoration: line-through;
        }
      }

      &.script {
        color: var(--td-warning-color);
      }

      &.visual {
        color: var(--td-success-color);
      }

      .value-icon {
        font-size: 12px;
        flex-shrink: 0;
      }

      .error-icon {
        color: var(--td-error-color);
        font-size: 12px;
        flex-shrink: 0;
      }
    }
  }

  .type-badge {
    flex-shrink: 0;
    padding: 2px 6px;
    background: var(--td-bg-color-tag);
    color: var(--td-text-color-secondary);
    font-size: 12px;
    border-radius: 3px;
    line-height: 1;
  }
}
</style>
