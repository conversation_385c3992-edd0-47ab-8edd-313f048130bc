import { FlowData, FlowDataValue, VariableReference } from '../model';

/**
 * 变量引用管理器
 * 负责管理变量引用关系，提供实时解析和同步更新功能
 */
export class VariableReferenceManager {
  private static instance: VariableReferenceManager;

  // 变量引用映射表：variableId -> Set<引用位置>
  private referenceMap = new Map<string, Set<string>>();

  // 变量信息缓存：variableId -> VariableReference
  private variableCache = new Map<string, VariableReference>();

  // 引用位置映射：引用位置 -> FlowDataValue
  private valueMap = new Map<string, FlowDataValue>();

  // 变更监听器
  private changeListeners = new Set<(variableId: string, reference: VariableReference) => void>();

  static getInstance(): VariableReferenceManager {
    if (!this.instance) {
      this.instance = new VariableReferenceManager();
    }
    return this.instance;
  }

  /**
   * 注册变量引用
   * @param valueId 引用位置的唯一标识
   * @param dataValue FlowDataValue对象
   * @param variableData 变量数据
   */
  registerReference(valueId: string, dataValue: FlowDataValue, variableData: FlowData): void {
    if (dataValue.type !== 'variable' || !variableData) return;

    const variableId = this.generateVariableId(variableData);
    const reference: VariableReference = {
      id: variableId,
      path: variableData.path || variableData.key,
      type: variableData.type,
      name: variableData.pathDescription || variableData.description || variableData.key,
      description: variableData.description,
      source: this.getVariableSource(dataValue.variableType),
    };

    // 更新引用映射
    if (!this.referenceMap.has(variableId)) {
      this.referenceMap.set(variableId, new Set());
    }
    this.referenceMap.get(variableId)!.add(valueId);

    // 更新变量缓存
    this.variableCache.set(variableId, reference);

    // 更新引用位置映射
    this.valueMap.set(valueId, dataValue);

    // 设置引用ID和路径
    dataValue.variableRefId = variableId;
    dataValue.variableRefPath = reference.path;
  }

  /**
   * 移除变量引用
   * @param valueId 引用位置的唯一标识
   */
  unregisterReference(valueId: string): void {
    const dataValue = this.valueMap.get(valueId);
    if (!dataValue || !dataValue.variableRefId) return;

    const variableId = dataValue.variableRefId;
    const references = this.referenceMap.get(variableId);

    if (references) {
      references.delete(valueId);
      if (references.size === 0) {
        this.referenceMap.delete(variableId);
        this.variableCache.delete(variableId);
      }
    }

    this.valueMap.delete(valueId);
  }

  /**
   * 获取变量引用信息
   * @param variableId 变量ID
   */
  getVariableReference(variableId: string): VariableReference | undefined {
    return this.variableCache.get(variableId);
  }

  /**
   * 获取变量引用信息（通过FlowDataValue）
   * @param dataValue FlowDataValue对象
   * @param availableVariables 可用的变量列表，用于自动初始化
   */
  getVariableReferenceByValue(
    dataValue: FlowDataValue,
    availableVariables?: FlowData[],
  ): VariableReference | undefined {
    if (dataValue.type !== 'variable') return undefined;

    // 如果已有引用ID，检查引用是否仍然有效
    if (dataValue.variableRefId) {
      const existingReference = this.getVariableReference(dataValue.variableRefId);
      if (existingReference) {
        return existingReference;
      } else {
        // 引用失效，清除引用ID，尝试重新解析
        console.log('变量引用失效，尝试重新解析:', dataValue.variableRefId);
        dataValue.variableRefId = undefined;
      }
    }

    // 如果没有引用ID但有variableValue，尝试自动初始化
    if (dataValue.variableValue && availableVariables) {
      const matchedVariable = this.findVariableByPath(dataValue.variableValue, availableVariables);
      if (matchedVariable) {
        // 自动注册引用
        const valueId = this.generateValueId();
        this.registerReference(valueId, dataValue, matchedVariable);
        return this.getVariableReference(dataValue.variableRefId!);
      } else {
        console.log('无法找到匹配的变量:', {
          variableValue: dataValue.variableValue,
          variableType: dataValue.variableType,
          availableCount: availableVariables.length,
        });
      }
    }

    return undefined;
  }

  /**
   * 更新变量信息
   * @param oldVariableData 旧的变量数据
   * @param newVariableData 新的变量数据
   */
  updateVariable(oldVariableData: FlowData, newVariableData: FlowData): void {
    const oldVariableId = this.generateVariableId(oldVariableData);
    const newVariableId = this.generateVariableId(newVariableData);

    const references = this.referenceMap.get(oldVariableId);
    if (!references) return;

    const newReference: VariableReference = {
      id: newVariableId,
      path: newVariableData.path || newVariableData.key,
      type: newVariableData.type,
      name: newVariableData.pathDescription || newVariableData.description || newVariableData.key,
      description: newVariableData.description,
      source: this.variableCache.get(oldVariableId)?.source || 'current',
    };

    // 如果变量ID发生变化，需要更新映射
    if (oldVariableId !== newVariableId) {
      this.referenceMap.set(newVariableId, references);
      this.referenceMap.delete(oldVariableId);
      this.variableCache.delete(oldVariableId);
    }

    // 更新变量缓存
    this.variableCache.set(newVariableId, newReference);

    // 更新所有引用该变量的FlowDataValue
    references.forEach((valueId) => {
      const dataValue = this.valueMap.get(valueId);
      if (dataValue) {
        dataValue.variableRefId = newVariableId;
        dataValue.variableRefPath = newReference.path;
        dataValue.dataType = newReference.type;
        dataValue.variableName = newReference.name;
        dataValue.variableValue = newReference.path;
      }
    });

    // 通知监听器
    this.notifyChange(newVariableId, newReference);
  }

  /**
   * 删除变量
   * @param variableData 变量数据
   */
  deleteVariable(variableData: FlowData): void {
    const variableId = this.generateVariableId(variableData);
    const references = this.referenceMap.get(variableId);

    if (references) {
      // 清空所有引用该变量的FlowDataValue
      references.forEach((valueId) => {
        const dataValue = this.valueMap.get(valueId);
        if (dataValue) {
          // 重置为文本类型
          dataValue.type = 'text';
          dataValue.textValue = '';
          dataValue.variableRefId = undefined;
          dataValue.variableRefPath = undefined;
          dataValue.variableType = '';
          dataValue.variableName = '';
          dataValue.variableValue = '';
          dataValue.dataType = '';
        }
      });

      this.referenceMap.delete(variableId);
      this.variableCache.delete(variableId);
    }
  }

  /**
   * 添加变更监听器
   * @param listener 监听器函数
   */
  addChangeListener(listener: (variableId: string, reference: VariableReference) => void): void {
    this.changeListeners.add(listener);
  }

  /**
   * 移除变更监听器
   * @param listener 监听器函数
   */
  removeChangeListener(listener: (variableId: string, reference: VariableReference) => void): void {
    this.changeListeners.delete(listener);
  }

  /**
   * 通知变更
   * @param variableId 变量ID
   * @param reference 变量引用信息
   */
  private notifyChange(variableId: string, reference: VariableReference): void {
    this.changeListeners.forEach((listener) => {
      try {
        listener(variableId, reference);
      } catch (error) {
        console.error('变量引用监听器执行错误:', error);
      }
    });
  }

  /**
   * 根据路径查找变量
   * @param path 变量路径
   * @param availableVariables 可用变量列表
   */
  private findVariableByPath(path: string, availableVariables: FlowData[]): FlowData | undefined {
    console.log('查找变量路径:', {
      path,
      availableVariablesCount: availableVariables.length,
      availableVariables: availableVariables.map((v) => ({
        key: v.key,
        path: v.path,
        type: v.type,
        hasChildren: !!(v.children && v.children.length > 0),
      })),
    });

    // 递归查找变量
    const findInVariables = (variables: FlowData[], level = 0): FlowData | undefined => {
      for (const variable of variables) {
        const indent = '  '.repeat(level);
        console.log(`${indent}检查变量:`, {
          key: variable.key,
          path: variable.path,
          targetPath: path,
          pathMatch: variable.path === path,
          keyMatch: variable.key === path,
        });

        // 检查当前变量的路径和键
        // 优先匹配 path，如果 path 不存在则匹配 key
        const variablePath = variable.path || variable.key;
        if (variablePath === path || variable.key === path || variable.path === path) {
          console.log(`${indent}找到匹配变量:`, variable);
          return variable;
        }
        // 递归检查子变量
        if (variable.children && variable.children.length > 0) {
          const found = findInVariables(variable.children, level + 1);
          if (found) return found;
        }
      }
      return undefined;
    };

    const result = findInVariables(availableVariables);
    console.log('变量查找结果:', {
      path,
      found: !!result,
      result: result
        ? {
            key: result.key,
            path: result.path,
            type: result.type,
          }
        : null,
    });

    return result;
  }

  /**
   * 生成值ID
   */
  private generateValueId(): string {
    return `value_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 生成变量ID
   * @param variableData 变量数据
   */
  private generateVariableId(variableData: FlowData): string {
    return `${variableData.id}_${variableData.key}_${variableData.path || ''}`;
  }

  /**
   * 获取变量来源
   * @param variableType 变量类型
   */
  private getVariableSource(variableType?: string): 'current' | 'local' | 'global' {
    switch (variableType) {
      case 'current':
        return 'current';
      case 'local':
        return 'local';
      case 'global':
        return 'global';
      default:
        return 'current';
    }
  }

  /**
   * 清空所有引用
   */
  clear(): void {
    this.referenceMap.clear();
    this.variableCache.clear();
    this.valueMap.clear();
    this.changeListeners.clear();
  }

  /**
   * 获取调试信息
   */
  getDebugInfo() {
    return {
      referenceCount: this.referenceMap.size,
      variableCount: this.variableCache.size,
      valueCount: this.valueMap.size,
      listenerCount: this.changeListeners.size,
      references: Array.from(this.referenceMap.entries()).map(([variableId, valueIds]) => ({
        variableId,
        valueIds: Array.from(valueIds),
        variable: this.variableCache.get(variableId),
      })),
    };
  }
}

// 导出单例实例
export const variableReferenceManager = VariableReferenceManager.getInstance();
