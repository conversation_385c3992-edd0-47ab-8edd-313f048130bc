import { cloneDeep } from 'lodash-es';
import { defineStore } from 'pinia';

import {
  ActionConfig,
  FlowControl,
  FlowData,
  FlowDataValue,
  FlowInfo,
  FlowStep,
} from '@/components/action-panel/model';
import { useApiStore } from '@/pages/service/apis/store';
import { processVariableList } from '../utils/variableProcessor';

import ActionBlank from '../ActionBlank.vue';
import { getComponent } from '../actions';
import { useDataQueryStore } from '../actions/1_DataBase/1_DataCustomizeQuery/store';
import { useDataAutoQueryStore } from '../actions/1_DataBase/1_DataQuery/store';
import { useDataSaveStore } from '../actions/1_DataBase/2_DataSave/store';
import { useApiRequestStore } from '../actions/2_Network/1_ApiRequest/store';
import { useApiRequestV2Store } from '../actions/2_Network/1_ApiRequestV2/store';
import { useApiForwarderStore } from '../actions/2_Network/2_ApiForwarder/store';
import { useWhileStore } from '../actions/4_Control/2_Loop/store';
import { useForEachStore } from '../actions/4_Control/3_ForEach/store';
import { getActionList, getVariableList, moveAction, recursionMergeVariable, removeAction, setNextId } from '../utils';

const recursionFindVariable = (path: string, list: FlowData[]) => {
  const variable = list.find((item) => item.path === path);
  if (variable) return variable;

  for (const item of list) {
    if (item.children && Array.isArray(item.children)) {
      const found = recursionFindVariable(path, item.children);
      if (found) return found;
    }
  }
  return null;
};

const initialState = (state) => {
  state.flowType = 'JOB';
  state.flowTitle = '';
  state.flowInfo = {
    id: '',
    version: 0,
    data: [],
    body: [],
  } as FlowInfo;
  state.currentStep = {} as FlowStep;
  state.flowTab = 1;
  state.flowVariables = [] as FlowData[];
  state.showMoveActionDialog = false;
  state.isSaveValue = false;
  state.showValueDialog = false;
  state.currentValueInputData = null;
  state.isSaveBatchVariable = false;
  state.showBatchVariableDialog = false;
  state.currentVariableData = null;
};

export const useActionFlowStore = defineStore('actionFlow', {
  state: () => {
    const state = {
      flowType: 'JOB',
      flowTitle: '',
      flowInfo: null,
      currentStep: null,
      showMoveActionDialog: false,
      flowTab: 0,
      flowVariables: [],
      isSaveValue: false,
      showValueDialog: false,
      currentValueInputData: null,
      isSaveBatchVariable: false,
      showBatchVariableDialog: false,
      currentVariableData: null,
      showFlowConfigDialog: false,
    } as {
      flowType: 'JOB' | 'API' | 'EVENT';
      flowTitle: string;
      flowInfo: FlowInfo;
      currentStep: FlowStep;
      showMoveActionDialog?: boolean;
      flowTab: number;
      flowVariables: FlowData[];
      isSaveValue?: boolean;
      showValueDialog?: boolean;
      currentValueInputData?: {
        id: string;
        value: FlowDataValue;
        onlyVariable?: boolean;
        limitTypes?: string[];
      };
      isSaveBatchVariable?: boolean;
      showBatchVariableDialog?: boolean;
      currentVariableData?: FlowData;
      showFlowConfigDialog?: boolean;
    };
    initialState(state);
    return state;
  },
  getters: {
    localVariables(state) {
      if (state.currentStep?.function === 'apiInfo') {
        return [];
      }
      if (this.flowTab === 0) {
        return [];
      }
      return getVariableList(state.currentStep, state.flowInfo) as FlowData[];
    },
    currentVariables(state) {
      if (state.currentStep?.function === 'apiInfo') {
        const apiStore = useApiStore();
        return apiStore.variables;
      }

      if (this.flowTab === 0) {
        return [...this.flowVariables];
      }
      const dataQueryStore = useDataQueryStore();
      const dataAutoQueryStore = useDataAutoQueryStore();
      const dataSaveStore = useDataSaveStore();
      const apiRequestStore = useApiRequestStore();
      const apiRequest2Store = useApiRequestV2Store();
      const apiForwarderStore = useApiForwarderStore();
      const forEachStore = useForEachStore();
      const loopStore = useWhileStore();

      switch (state.currentStep?.function) {
        case 'dataCustomizeQuery':
          return dataQueryStore.variables;
        case 'dataQuery':
          return dataAutoQueryStore.variables;
        case 'dataSave':
          return dataSaveStore.variables;
        case 'apiRequest':
          return apiRequestStore.variables;
        case 'apiRequestV2':
          return apiRequest2Store.variables;
        case 'apiForwarder':
          return apiForwarderStore.variables;
        case 'dataForEach':
          return forEachStore.variables;
        case 'controlLoop':
          return loopStore.variables;
        default:
          return [];
      }
    },
    // 处理后的本地变量（扁平化，包含正确的路径信息）
    processedLocalVariables() {
      return processVariableList(this.localVariables);
    },
    // 处理后的当前变量（扁平化，包含正确的路径信息）
    processedCurrentVariables() {
      return processVariableList(this.currentVariables);
    },
    // 根据变量类型获取处理后的变量列表
    getProcessedVariablesByType() {
      return (variableType?: string, globalVariables: FlowData[] = []) => {
        const processedGlobalVariables = processVariableList(globalVariables);

        switch (variableType) {
          case 'current':
            return this.processedCurrentVariables || [];
          case 'local':
            return this.processedLocalVariables || [];
          case 'global':
            return processedGlobalVariables;
          default:
            // 如果没有指定类型，返回所有可用变量
            return [
              ...(this.processedCurrentVariables || []),
              ...(this.processedLocalVariables || []),
              ...processedGlobalVariables,
            ];
        }
      };
    },
    noAction(state) {
      return state.flowInfo.body.length === 0;
    },
    startId(state) {
      if (this.noAction) return null;
      return state.flowInfo.body[0]?.id ?? null;
    },
    currentActionTitle(state) {
      return state.currentStep?.name ? `配置 - ${state.currentStep.name}` : '';
    },
    currentView(state) {
      if (this.noAction) return ActionBlank;
      return getComponent(state.currentStep?.config?.componentName) || ActionBlank;
    },
  },
  actions: {
    resetFlowInfo() {
      initialState(this);
    },
    setFlowVariables(flowVariables: FlowData[]) {
      this.flowVariables = flowVariables;
    },
    setFlowInfo(flowInfo: FlowInfo) {
      if (flowInfo.body.length > 0 && !flowInfo.body.some((item) => item.id === this.currentStep.id)) {
        setTimeout(() => {
          this.setCurrentStep(flowInfo.body[0]);
        }, 300);
      }

      this.flowInfo = flowInfo;
    },
    setFlowData(flowData: FlowData[]) {
      this.flowInfo.data = flowData;
    },
    setFlowBody(flowBody: FlowStep[]) {
      this.flowInfo.body = flowBody;
    },
    addFlowStep(flowStep: FlowStep) {
      if (this.flowInfo.body.some((item) => item.id === flowStep.id)) return;
      this.flowInfo.body.push(flowStep);
    },
    setStepNextId(parentNextId: string, currentId: string, isNest: boolean) {
      setNextId(parentNextId, currentId, this.flowInfo.body, isNest);
    },
    getActionListByStartId(id: string) {
      return getActionList(id, this.flowInfo.body);
    },
    removeFlowStep(id: string) {
      this.setFlowBody(removeAction(id, this.flowInfo.body));
    },
    setCurrentStep(currentStep: FlowStep) {
      this.currentStep = currentStep;
    },
    setCurrentApiStep() {
      this.currentStep = {
        id: '__default__api_info__',
        name: '接口信息',
        function: 'apiInfo',
      };
    },
    setStepResultData(resultData: FlowData[]) {
      if (!this.currentStep.result || this.currentStep.result.length === 0) {
        this.currentStep.result = resultData;
        return;
      }

      const rootNode = this.currentStep.result.find((item) => item.key === 'ROOT');
      if (rootNode && this.currentStep.result.length > 1) {
        this.currentStep.result = [rootNode];
      }

      const tempResultData = cloneDeep(this.currentStep.result);
      recursionMergeVariable(tempResultData, resultData);

      if (rootNode && tempResultData[0].children.length == 1) {
        this.currentStep.result = tempResultData[0].children;
      } else {
        this.currentStep.result = tempResultData;
      }
    },
    removeStepResultData(key: string) {
      if (!this.currentStep.result || this.currentStep.result.length === 0) {
        return;
      }
      this.currentStep.result = this.currentStep.result.filter((item) => item.key !== key);
    },
    setCurrentName(name: string) {
      this.currentStep.name = name;
    },
    setCurrentArgs(args: any) {
      this.currentStep.args = args;
    },
    setCurrentControl(control: FlowControl) {
      this.currentStep.control = control;
    },
    setCurrentConfig(config: ActionConfig) {
      this.currentStep.config = config;
    },
    getVariableData(path: string) {
      return recursionFindVariable(path, this.localVariables);
    },
    moveStep(source: FlowStep, target: FlowStep, position: 'before' | 'inside' | 'after') {
      moveAction(source, target, position, this.flowInfo.body);
    },
  },
});
