<template>
  <t-dialog
    v-model:visible="showValueDialog"
    attach="body"
    header="选择值"
    :width="data?.type === 'visual' ? '80%' : '50%'"
    height="80vh"
    top="10vh"
    :footer="false"
    :dialog-style="{ transition: 'width 0.3s ease-in-out', 'min-width': '900px' }"
  >
    <div v-if="data" class="value-container">
      <div v-if="!onlyVariable" class="header">
        <t-space>
          <div class="title">类型</div>
          <t-radio-group v-model:value="data.type" variant="primary-filled">
            <t-radio-button
              v-for="item in valueTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></t-radio-button>
          </t-radio-group>
          <!-- <t-select v-model:value="data.type" :options="valueTypeOptions" placeholder="请选择类型"></t-select> -->
          <t-input v-if="data.type === 'script'" v-model="data.scriptName" placeholder="脚本名称"></t-input>
        </t-space>
        <div v-if="data.type !== 'variable'" class="toolbar-right">
          <t-button @click="onClickSave">保存</t-button>
        </div>
      </div>
      <!-- only-variable 模式下的清空按钮 -->
      <div v-if="onlyVariable && data.variableValue" class="header">
        <t-space>
          <div v-if="data.variableValue" class="selected-variable">
            已选择: {{ data.variableName || data.variableValue }}
          </div>
        </t-space>
        <div class="toolbar-right">
          <t-button v-if="data.variableValue" theme="default" variant="outline" @click="onClickClear"
            >清空选择</t-button
          >
        </div>
      </div>
      <div v-if="data.type === 'text' || data.type === 'script' || data.type === 'visual'" class="content">
        <!-- 调试信息 -->
        <!-- <div v-if="data.type === 'text'" class="debug-info">
          <t-space>
            <t-select
              v-model="data.dataType"
              :options="dataTypeOptions"
              placeholder="选择数据类型"
              size="small"
              style="width: 200px"
            />
          </t-space>
        </div> -->

        <data-type-input
          v-show="data.type === 'text'"
          v-model="data.textValue"
          :data-type="data.dataType"
          placeholder="请输入文本"
        />
        <!-- 保留原有的 textarea 作为备用 -->
        <!-- <t-textarea
          v-show="data.type === 'text'"
          v-model="data.textValue"
          placeholder="请输入文本"
          :autosize="{ minRows: 5, maxRows: 12 }"
        ></t-textarea> -->
        <script-editor
          v-if="data.type === 'script'"
          ref="editorRef"
          v-model="data.scriptValue"
          :language="codeLanguage"
          :enable-intellisense="true"
          :current-variables="actionFlowStore.currentVariables"
          :local-variables="actionFlowStore.localVariables"
          :global-variables="actionFlowStore.globalVariables"
          :functions="functionList"
          editor-style="height: 250px"
        />
        <div v-show="data.type === 'visual'" class="visual-function-container">
          <visual-function-composer
            :key="`visual-composer-${currentValueInputData?.id || 'default'}`"
            v-model="data.visualSteps"
            :input-data="sampleInputData"
            @test="onTestVisualFunction"
            @change="onVisualFunctionChange"
            style="height: 650px"
          />
        </div>
      </div>
      <div v-if="data.type == 'script' || data.type == 'variable'" class="footer">
        <t-tabs v-model="activeTab" @change="onChangeTab">
          <t-tab-panel :value="0" label="临时变量">
            <variable-tree
              ref="currentVariableTreeRef"
              v-model:active-id="data.variableValue"
              :filter-text="searchText"
              :variable-list="actionFlowStore.currentVariables"
              :limit-types="limitTypes"
              @dblclick="onDblclickVariable"
            ></variable-tree>
          </t-tab-panel>
          <t-tab-panel :value="1" label="局部变量">
            <variable-tree
              ref="localVariableTreeRef"
              v-model:active-id="data.variableValue"
              :filter-text="searchText"
              :variable-list="actionFlowStore.localVariables"
              :limit-types="limitTypes"
              @dblclick="onDblclickVariable"
            ></variable-tree>
          </t-tab-panel>
          <t-tab-panel :value="2" label="全局变量">
            <variable-tree
              v-model:active-id="data.variableValue"
              :filter-text="searchText"
              :variable-list="globalVariables"
              :limit-types="limitTypes"
              @dblclick="onDblclickVariable"
            ></variable-tree>
          </t-tab-panel>
          <t-tab-panel v-if="data.type === 'script'" :value="3" label="函数">
            <enhanced-function-list @dblclick="onDblclickFunction"></enhanced-function-list>
          </t-tab-panel>
          <template #action>
            <t-input v-model="searchText" class="search-input" size="small" placeholder="搜索" clearable>
              <template #prefixIcon>
                <search-icon></search-icon>
              </template>
            </t-input>
          </template>
        </t-tabs>
      </div>
    </div>
  </t-dialog>
</template>
<script lang="ts">
export default {
  name: 'ValueDialog',
};
</script>
<script setup lang="ts">
import { isEmpty } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { SearchIcon } from 'tdesign-icons-vue-next';
import { computed, nextTick, onActivated, onMounted, ref, watch } from 'vue';

import { useActionFlowStore } from '@/components/action-panel/store/index';
import ScriptEditor from '@/components/editor/ScriptEditor.vue';
import { getAllFunctions, type FunctionItem } from '@/composables/services/functionDataService';

import DataTypeInput from './DataTypeInput.vue';
import EnhancedFunctionList from './EnhancedFunctionList.vue';
import { FlowData, FlowDataValue } from './model';
import VariableTree from './VariableTree.vue';
import VisualFunctionComposer from './VisualFunctionComposer.vue';
import { api, Services } from '@/api/system';
import { VALUE_TYPE_MAP } from './constants';
import { useVariableSelection } from './composables/useVariableReference';

const actionFlowStore = useActionFlowStore();

const { currentValueInputData, showValueDialog } = storeToRefs(actionFlowStore);

// 转换后端数据为前端格式
const convertVisualDataFromBackend = (dataValue: any) => {
  if (!dataValue) return dataValue;

  // 深拷贝数据，避免引用问题
  const clonedData = JSON.parse(JSON.stringify(dataValue));

  if (clonedData?.type === 'visual') {
    // 处理 visual 类型的数据
    if (clonedData.visualSteps) {
      // 如果visualSteps是字符串，需要解析
      if (typeof clonedData.visualSteps === 'string') {
        try {
          const steps = JSON.parse(clonedData.visualSteps);
          // 确保steps是数组
          if (Array.isArray(steps)) {
            // 转换回前端格式
            const convertedSteps = steps.map((step: any) => ({
              id: step.id,
              functionName: step.functionName,
              functionType: step.functionType === 0 ? 'builtin' : step.functionType === 1 ? 'csharp' : 'javascript',
              displayName: step.displayName,
              description: step.description,
              parameters:
                step.parameters?.map((param: any) => ({
                  name: param.name,
                  type: param.type === 0 ? 'text' : param.type === 1 ? 'variable' : 'previousResult',
                  value: param.value,
                  required: param.required,
                })) || [],
              outputVariable: step.outputVariable,
              order: step.order,
            }));
            clonedData.visualSteps = convertedSteps;
          } else {
            console.warn('visualSteps解析后不是数组:', steps);
            clonedData.visualSteps = [];
          }
        } catch (error) {
          console.error('解析visual步骤失败:', error);
          clonedData.visualSteps = [];
        }
      } else if (Array.isArray(clonedData.visualSteps)) {
        // 如果已经是数组，确保数据格式正确，并处理后端的数字枚举格式
        clonedData.visualSteps = clonedData.visualSteps.map((step: any) => ({
          id: step.id || `step_${Date.now()}_${Math.random()}`,
          functionName: step.functionName || '',
          functionType:
            typeof step.functionType === 'number'
              ? step.functionType === 0
                ? 'builtin'
                : step.functionType === 1
                  ? 'csharp'
                  : 'javascript'
              : step.functionType || 'builtin',
          displayName: step.displayName || step.functionName || '',
          description: step.description || '',
          parameters:
            step.parameters?.map((param: any) => ({
              name: param.name || '',
              type:
                typeof param.type === 'number'
                  ? param.type === 0
                    ? 'text'
                    : param.type === 1
                      ? 'variable'
                      : 'previousResult'
                  : param.type || 'text',
              value: param.value || '',
              required: param.required || false,
            })) || [],
          outputVariable: step.outputVariable || '',
          order: step.order || 0,
        }));
      } else {
        // 其他情况重置为空数组
        clonedData.visualSteps = [];
      }
    } else {
      // 如果没有 visualSteps，初始化为空数组
      clonedData.visualSteps = [];
    }
  } else {
    // 如果不是 visual 类型，确保 visualSteps 为空数组（避免残留数据）
    clonedData.visualSteps = [];
  }

  return clonedData;
};

const globalVariables = ref<FlowData[]>([]);

// 函数列表数据，用于智能提示
const functionList = ref<FunctionItem[]>([]);

// 加载函数数据
const loadFunctions = async () => {
  try {
    const functions = await getAllFunctions();
    functionList.value = functions;
    console.log(`ValueDialog: 已加载 ${functions.length} 个函数`);
  } catch (error) {
    console.error('ValueDialog: 加载函数数据失败:', error);
    functionList.value = [];
  }
};

// 初始化数据
const initializeData = async () => {
  globalVariables.value = await getGlobalVariables();
  await loadFunctions();
};

onActivated(initializeData);
onMounted(initializeData);

const data = ref<FlowDataValue>(convertVisualDataFromBackend(currentValueInputData.value?.value));
const onlyVariable = computed(() => currentValueInputData.value?.onlyVariable);
const limitTypes = computed(() => currentValueInputData.value?.limitTypes);

// const emits = defineEmits(['variable-change']);

const onClickSave = () => {
  console.log('ValueDialog onClickSave - before conversion:', {
    type: data.value?.type,
    visualStepsType: typeof data.value?.visualSteps,
    visualStepsLength: Array.isArray(data.value?.visualSteps) ? data.value?.visualSteps.length : 'not array',
  });

  // 对于visual类型，需要转换数据格式
  const valueToSave = convertVisualDataForBackend(data.value);

  console.log('ValueDialog onClickSave - after conversion:', {
    type: valueToSave?.type,
    visualStepsType: typeof valueToSave?.visualSteps,
    visualStepsIsString: typeof valueToSave?.visualSteps === 'string',
    visualStepsContent:
      typeof valueToSave?.visualSteps === 'string' ? valueToSave.visualSteps.substring(0, 100) + '...' : 'not string',
  });

  actionFlowStore.currentValueInputData.value = valueToSave;
  actionFlowStore.showValueDialog = false;
  actionFlowStore.isSaveValue = true;
};

const onClickClear = () => {
  // 清空变量选择
  data.value.variableType = '';
  data.value.variableName = '';
  data.value.variableValue = '';
  data.value.dataType = '';

  // 保存清空后的数据
  onClickSave();
};

const searchText = ref('');
const valueTypeOptions = computed(() => {
  return [
    { label: '纯文本', value: 'text' },
    { label: '变量', value: 'variable' },
    { label: '动态脚本', value: 'script' },
    { label: '可视化函数', value: 'visual' },
  ];
});

// 数据类型选项
const dataTypeOptions = computed(() => {
  return Object.keys(VALUE_TYPE_MAP).map((key) => ({
    label: VALUE_TYPE_MAP[key],
    value: key,
  }));
});

const codeLanguage = computed(() => {
  if (data.value.type === 'script') {
    return 'typescript';
  }
  return 'plaintext';
});

const localVariableTreeRef = ref();
const currentVariableTreeRef = ref();

const activeTab = ref(0);

const onChangeTab = (value: any) => {
  nextTick(() => {
    if (value === 0) {
      currentVariableTreeRef.value.scrollToActive();
    } else if (value === 1) {
      localVariableTreeRef.value.scrollToActive();
    }
  });
};

watch(showValueDialog, (val) => {
  if (val) {
    setActiveTab();
  }
});

// 移除自动监听，改为手动刷新

watch(
  () => data.value?.type,
  (val) => {
    if (val === 'variable' && activeTab.value >= 2) {
      activeTab.value = 0;
    }
  },
);

const setActiveTab = () => {
  const valData = currentValueInputData.value?.value;
  if (!valData || valData.type !== 'variable') return;
  if (valData.type === 'variable') {
    if (valData.variableType === 'current') {
      activeTab.value = 0;
    } else if (valData.variableType === 'local') {
      activeTab.value = 1;
    } else {
      activeTab.value = 2;
    }
    onChangeTab(activeTab.value);
  }
};

watch(
  currentValueInputData,
  (newValue) => {
    console.log('ValueDialog watch currentValueInputData:', {
      newValue: newValue?.value,
      type: newValue?.value?.type,
      visualStepsType: typeof newValue?.value?.visualSteps,
      visualStepsLength: Array.isArray(newValue?.value?.visualSteps)
        ? newValue?.value?.visualSteps.length
        : 'not array',
    });

    if (newValue?.value) {
      // 确保对visual类型的数据进行正确的转换
      const convertedData = convertVisualDataFromBackend(newValue.value);
      console.log('ValueDialog converted data:', {
        type: convertedData?.type,
        visualStepsType: typeof convertedData?.visualSteps,
        visualStepsLength: Array.isArray(convertedData?.visualSteps) ? convertedData?.visualSteps.length : 'not array',
      });
      data.value = convertedData;
    } else {
      data.value = newValue?.value;
    }
  },
  {
    deep: true,
  },
);

watch(
  () => currentValueInputData.value?.value?.type,
  (val) => {
    if (val === 'variable') {
      setActiveTab();
    } else if (val === 'script') {
      activeTab.value = 3;
    }
  },
);

const editorRef = ref();
const onDblclickFunction = (func: any) => {
  editorRef.value.insertText(func.script);
  if (isEmpty(data.value.scriptName)) data.value.scriptName = func.label;
};

// 可视化函数相关
const sampleInputData = computed(() => {
  // 从当前变量中构建示例数据
  const inputData: any = {};
  actionFlowStore.currentVariables.forEach((variable) => {
    if (variable.path) {
      const keys = variable.path.split('.');
      let current = inputData;
      for (let i = 0; i < keys.length - 1; i++) {
        if (!current[keys[i]]) {
          current[keys[i]] = {};
        }
        current = current[keys[i]];
      }
      current[keys[keys.length - 1]] =
        variable.type === 'string'
          ? 'sample text'
          : variable.type === 'number'
            ? 123
            : variable.type === 'boolean'
              ? true
              : null;
    }
  });
  return inputData;
});

// 转换前端数据结构为后端期望的格式
const convertStepsForBackend = (steps: any[]) => {
  return steps.map((step) => ({
    id: step.id,
    functionName: step.functionName,
    functionType: step.functionType === 'builtin' ? 0 : step.functionType === 'csharp' ? 1 : 2,
    displayName: step.displayName,
    description: step.description,
    parameters: step.parameters.map((param: any) => ({
      name: param.name,
      type: param.type === 'text' ? 0 : param.type === 'variable' ? 1 : 2,
      value: param.value,
      required: param.required,
    })),
    outputVariable: step.outputVariable,
    order: step.order,
  }));
};

const onTestVisualFunction = async (steps: any[]) => {
  try {
    // 转换数据格式
    const convertedSteps = convertStepsForBackend(steps);

    // 调用后端API进行测试
    const response = await api.run(Services.visualFunctionExecute, {
      steps: convertedSteps,
      inputData: sampleInputData.value,
      generateExpressionOnly: false,
    });

    console.log('可视化函数测试结果:', response);

    // 可以在这里显示测试结果的弹窗或通知
    if (response.success !== false) {
      console.log('执行成功，结果:', response.result);
    } else {
      console.error('执行失败:', response.errorMessage);
    }
  } catch (error) {
    console.error('测试失败:', error);
  }
};

const onVisualFunctionChange = (steps: any[]) => {
  console.log('ValueDialog onVisualFunctionChange:', {
    stepsLength: steps?.length,
    stepsType: typeof steps,
    isArray: Array.isArray(steps),
  });

  if (data.value) {
    data.value.visualSteps = steps;
    // 生成可视化函数的名称
    if (steps.length > 0) {
      data.value.visualName = `可视化函数(${steps.length}步)`;
    } else {
      data.value.visualName = '';
    }
  }
};

// 转换visual数据为后端格式
const convertVisualDataForBackend = (dataValue: any) => {
  if (!dataValue) return dataValue;

  // 深拷贝数据，避免修改原始数据
  const clonedData = JSON.parse(JSON.stringify(dataValue));

  if (clonedData.type === 'visual' && clonedData.visualSteps) {
    // 如果是数组，转换为后端格式（对象数组，不序列化为字符串）
    if (Array.isArray(clonedData.visualSteps)) {
      try {
        const convertedSteps = convertStepsForBackend(clonedData.visualSteps);
        clonedData.visualSteps = convertedSteps; // 直接使用对象数组，不转换为字符串
      } catch (error) {
        console.error('转换visual步骤失败:', error);
        clonedData.visualSteps = [];
      }
    } else if (typeof clonedData.visualSteps === 'string') {
      // 如果是字符串，解析为对象数组
      try {
        const steps = JSON.parse(clonedData.visualSteps);
        if (Array.isArray(steps)) {
          clonedData.visualSteps = convertStepsForBackend(steps);
        } else {
          clonedData.visualSteps = [];
        }
      } catch (error) {
        console.error('解析visual步骤字符串失败:', error);
        clonedData.visualSteps = [];
      }
    } else {
      // 其他情况重置为空数组
      clonedData.visualSteps = [];
    }
  }

  return clonedData;
};

const onDblclickVariable = ({ path, item }) => {
  if (data.value.type === 'script') {
    const prefix = activeTab.value === 0 ? '' : '_data.';
    editorRef.value.insertText(prefix + path);
    return;
  }
  if (data.value.type === 'variable') {
    let variableType: 'current' | 'local' | 'global';
    if (activeTab.value === 0) {
      variableType = 'current';
    } else if (activeTab.value === 1) {
      variableType = 'local';
    } else {
      variableType = 'global';
    }

    // 使用新的变量选择处理逻辑
    const { handleVariableSelect } = useVariableSelection();
    handleVariableSelect(data.value, path, item, variableType);

    onClickSave();
  }
};
</script>
<style lang="less" scoped>
.search-input {
  position: relative;
  top: 8px;
  width: 180px;
}

.value-container {
  display: flex;
  flex-direction: column;
  height: 100%;

  > .header {
    padding: 8px 0;
    border-bottom: 1px solid var(--td-component-stroke);
    flex-shrink: 0; /* 防止头部被压缩 */

    .toolbar-right {
      float: right;
    }
  }

  > .content {
    flex: 1;
    padding: 8px 0;

    .debug-info {
      margin-bottom: 12px;
      padding: 8px;
      background-color: var(--td-bg-color-container-hover);
      border-radius: 6px;
      border: 1px dashed var(--td-border-level-2-color);
    }
  }

  > .footer {
    height: 450px;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    /* 确保 tabs 组件占用全部可用高度 */
    :deep(.t-tabs) {
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    /* tabs 头部固定，内容区域自适应 */
    :deep(.t-tabs__header) {
      flex-shrink: 0;
    }

    :deep(.t-tabs__content) {
      flex: 1;
      overflow: hidden;
    }

    /* tab 面板占用全部高度 */
    :deep(.t-tab-panel) {
      height: 100%;
      overflow: hidden;
    }

    /* 确保 variable-tree 组件占用全部高度 */
    :deep(.variable-tree) {
      height: 100%;
      padding-top: 8px;
    }

    /* 修复双重滚动条问题 - 确保只有内层树有滚动条 */
    :deep(.variable-tree .tree-container) {
      overflow: hidden;
    }

    :deep(.variable-tree .tree-container .t-tree) {
      height: 100%;
      overflow-y: auto;
      overflow-x: hidden;
      max-height: none !important;
    }

    /* 移除 TDesign 树组件的默认高度限制 */
    :deep(.variable-tree .t-tree__list) {
      max-height: none !important;
      height: auto !important;
    }

    :deep(.variable-tree .t-tree__scroll) {
      height: 100% !important;
      max-height: none !important;
    }

    /* 确保 tabs 内容区域不产生滚动条 */
    :deep(.t-tabs__content) {
      overflow: hidden !important;
    }

    :deep(.t-tab-panel) {
      overflow: hidden !important;
    }

    /* 确保变量树占用全部可用空间 */
    :deep(.variable-tree) {
      height: 100% !important;
      max-height: none !important;
    }

    /* 强制移除可能的高度限制 */
    :deep(.t-tree) {
      max-height: none !important;
    }

    :deep(.t-tree__inner) {
      max-height: none !important;
    }

    /* 确保只有在内容真正超出时才显示滚动条 */
    :deep(.variable-tree .tree-container .t-tree) {
      overflow-y: auto !important;
      overflow-x: hidden !important;
    }

    /* 确保 EnhancedFunctionList 组件占用全部高度 */
    :deep(.enhanced-function-list) {
      height: 100% !important;
      max-height: none !important;
    }

    /* 确保函数列表容器占用全部高度 */
    :deep(.enhanced-function-list .function-list-container) {
      height: 100% !important;
      max-height: none !important;
    }

    /* 确保函数详情面板可以滚动 */
    :deep(.enhanced-function-list .function-detail-panel) {
      overflow-y: auto !important;
      overflow-x: hidden !important;
    }

    /* 确保函数详情内容可以正常显示 */
    :deep(.enhanced-function-list .function-detail) {
      overflow-y: auto !important;
      overflow-x: hidden !important;
    }
  }
}

.selected-variable {
  color: var(--td-text-color-secondary);
  font-size: 12px;
  padding: 4px 8px;
  background-color: var(--td-bg-color-container-select);
  border-radius: 3px;
}

.visual-function-container {
  min-height: 400px;
  border: 1px solid var(--td-border-level-1-color);
  border-radius: 6px;
  overflow: hidden;
}
</style>
